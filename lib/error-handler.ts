import { NextResponse } from "next/server";

import {
  UserNotFoundError,
  UserAlreadyExistsError,
  EmailAlreadyTakenError,
  InvalidRolesError,
} from "@/lib/services/user";
import {
  InvalidVerificationTokenError,
  EmailAlreadyVerifiedError,
  EmailSendError,
} from "@/lib/services/email";
import {
  AuthenticationError,
  EmailNotVerifiedError,
  InvalidCredentialsError,
  PasswordMismatchError,
} from "@/lib/services/auth";
import {
  InvalidRoleError,
  InsufficientPermissionsError,
  AnalyticsDataError,
} from "@/lib/services/analytics";
import {
  AdNotFoundError,
  InvalidAppCredentialsError,
  NoAdsAvailableError,
  AdNotActiveError,
} from "@/lib/services/ad";
import {
  AppNotFoundError,
  AppNotActiveError as AppServiceNotActiveError,
} from "@/lib/services/app";

// Re-export service exceptions for use in other services
export {
  UserNotFoundError,
  UserAlreadyExistsError,
  EmailAlreadyTakenError,
  InvalidRolesError,
  InvalidVerificationTokenError,
  EmailAlreadyVerifiedError,
  EmailSendError,
  AuthenticationError,
  EmailNotVerifiedError,
  InvalidCredentialsError,
  PasswordMismatchError,
  InvalidRoleError,
  InsufficientPermissionsError,
  AnalyticsDataError,
  AdNotFoundError,
  InvalidAppCredentialsError,
  NoAdsAvailableError,
  AdNotActiveError,
  AppNotFoundError,
  AppServiceNotActiveError,
};

/**
 * Error handler utility for converting service layer exceptions
 * into appropriate HTTP responses
 */
export class ApiErrorHandler {
  /**
   * Convert service layer exceptions to HTTP responses
   */
  static handleServiceError(error: unknown): NextResponse {
    console.error("Service error:", error);

    // Handle known service exceptions
    if (error instanceof UserNotFoundError) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }

    if (error instanceof UserAlreadyExistsError) {
      return NextResponse.json({ error: error.message }, { status: 409 });
    }

    if (error instanceof EmailAlreadyTakenError) {
      return NextResponse.json({ error: error.message }, { status: 409 });
    }

    if (error instanceof InvalidRolesError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    if (error instanceof AuthenticationError) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    if (error instanceof EmailNotVerifiedError) {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }

    if (error instanceof InvalidCredentialsError) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    if (error instanceof PasswordMismatchError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    if (error instanceof InvalidVerificationTokenError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    if (error instanceof EmailAlreadyVerifiedError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    if (error instanceof EmailSendError) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (error instanceof InvalidRoleError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    if (error instanceof InsufficientPermissionsError) {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }

    if (error instanceof AnalyticsDataError) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (error instanceof AdNotFoundError) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }

    if (error instanceof InvalidAppCredentialsError) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    if (error instanceof NoAdsAvailableError) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }

    if (error instanceof AdNotActiveError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    if (error instanceof AppNotFoundError) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }

    if (error instanceof AppServiceNotActiveError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // Handle Prisma errors
    if (error && typeof error === "object" && "code" in error) {
      const prismaError = error as { code: string; message: string };

      switch (prismaError.code) {
        case "P2002": // Unique constraint violation
          return NextResponse.json(
            { error: "A record with this information already exists" },
            { status: 409 },
          );
        case "P2025": // Record not found
          return NextResponse.json(
            { error: "Record not found" },
            { status: 404 },
          );
        default:
          return NextResponse.json(
            { error: "Database operation failed" },
            { status: 500 },
          );
      }
    }

    // Handle generic errors
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Fallback for unknown errors
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }

  /**
   * Wrapper function for API route handlers to automatically handle service exceptions
   */
  static withErrorHandling<T extends any[], R>(
    handler: (...args: T) => Promise<NextResponse | R>,
  ) {
    return async (...args: T): Promise<NextResponse> => {
      try {
        const result = await handler(...args);

        if (result instanceof NextResponse) {
          return result;
        }

        // If handler returns data directly, wrap it in a NextResponse
        return NextResponse.json(result);
      } catch (error) {
        return ApiErrorHandler.handleServiceError(error);
      }
    };
  }
}
