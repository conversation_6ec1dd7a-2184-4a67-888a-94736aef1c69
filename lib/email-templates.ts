/**
 * Email templates for the application
 * Contains HTML and text templates for various email types
 */

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export interface EmailVerificationData {
  email: string;
  token: string;
  name?: string;
}

/**
 * Get verification URL for email verification
 */
export function getVerificationUrl(token: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

  return `${baseUrl}/verify-email?token=${token}`;
}

/**
 * Email verification template
 */
export function getEmailVerificationTemplate(
  data: EmailVerificationData,
): EmailTemplate {
  const { email, token, name } = data;
  const displayName = name || "there";
  const verificationUrl = getVerificationUrl(token);
  const subject = "Verify your email address - Mindify AiD Platform";

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Email Verification</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding: 20px 0; border-bottom: 2px solid #007bff; }
        .header h1 { color: #007bff; margin: 0; }
        .content { padding: 20px 0; }
        .button { display: inline-block; padding: 12px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .button:hover { background: #0056b3; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to Mindify AiD Platform!</h1>
          <p>Please verify your email address to get started</p>
        </div>
        <div class="content">
          <h2>Hi ${displayName},</h2>
          <p>Thank you for registering with Mindify AiD Platform! To complete your registration and start using our platform, please verify your email address by clicking the button below:</p>
          
          <div style="text-align: center;">
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
          </div>
          
          <div class="warning">
            <strong>Important:</strong> This verification link will expire in 24 hours. If you don't verify your email within this time, you'll need to register again.
          </div>
          
          <p>If you're unable to click the button above, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 5px;">${verificationUrl}</p>
          
          <p>If you didn't create an account with us, please ignore this email.</p>
        </div>
        <div class="footer">
          <p>Best regards,<br>The Mindify AiD Platform Team</p>
          <p>© 2025 Mindify AiD Platform. All rights reserved.<br>This is an automated email. Please do not reply to this message.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Welcome to Mindify AiD Platform!
    
    Hi ${displayName},
    
    Thank you for registering with Mindify AiD Platform! To complete your registration and start using our platform, please verify your email address by visiting this link:
    
    ${verificationUrl}
    
    Important: This verification link will expire in 24 hours. If you don't verify your email within this time, you'll need to register again.
    
    If you didn't create an account with us, please ignore this email.
    
    Best regards,
    The Mindify AiD Platform Team
    
    © 2025 Mindify AiD Platform. All rights reserved.
    This is an automated email. Please do not reply to this message.
  `;

  return { subject, html, text };
}

/**
 * Welcome email template
 */
export function getWelcomeEmailTemplate(name?: string): EmailTemplate {
  const displayName = name || "there";
  const subject = "Welcome to Mindify AiD Platform!";

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Mindify AiD Platform</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding: 20px 0; border-bottom: 2px solid #28a745; }
        .header h1 { color: #28a745; margin: 0; }
        .content { padding: 20px 0; }
        .button { display: inline-block; padding: 12px 30px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .button:hover { background: #1e7e34; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Welcome to Mindify AiD Platform!</h1>
          <p>Your email has been verified successfully</p>
        </div>
        <div class="content">
          <h2>Hi ${displayName},</h2>
          <p>Congratulations! Your email address has been verified and your account is now active.</p>

          <p>You can now:</p>
          <ul>
            <li>Access your dashboard</li>
            <li>Register AI applications (Model Providers)</li>
            <li>Create advertising campaigns (Advertisers)</li>
            <li>Start monetizing your AI applications</li>
          </ul>

          <div style="text-align: center;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard" class="button">Go to Dashboard</a>
          </div>

          <p>If you have any questions or need help getting started, feel free to reach out to our support team.</p>
        </div>
        <div class="footer">
          <p>Best regards,<br>The Mindify AiD Platform Team</p>
          <p>© 2025 Mindify AiD Platform. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Welcome to Mindify AiD Platform!

    Hi ${displayName},

    Congratulations! Your email address has been verified and your account is now active.

    You can now:
    - Access your dashboard
    - Register AI applications (Model Providers)
    - Create advertising campaigns (Advertisers)
    - Start monetizing your AI applications

    Visit your dashboard: ${process.env.NEXT_PUBLIC_APP_URL}/dashboard

    If you have any questions or need help getting started, feel free to reach out to our support team.

    Best regards,
    The Mindify AiD Platform Team

    © 2025 Mindify AiD Platform. All rights reserved.
  `;

  return { subject, html, text };
}
