/**
 * Centralized Error Management System
 * 
 * This file contains all custom error classes used throughout the application.
 * Errors are organized by domain/service for better maintainability.
 */

// ============================================================================
// USER SERVICE ERRORS
// ============================================================================

export class UserNotFoundError extends Error {
  constructor(message: string = "User not found") {
    super(message);
    this.name = "UserNotFoundError";
  }
}

export class UserAlreadyExistsError extends Error {
  constructor(message: string = "User with this email already exists") {
    super(message);
    this.name = "UserAlreadyExistsError";
  }
}

export class EmailAlreadyTakenError extends Error {
  constructor(message: string = "Email is already taken by another user") {
    super(message);
    this.name = "EmailAlreadyTakenError";
  }
}

export class InvalidRolesError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "InvalidRolesError";
  }
}

// ============================================================================
// EMAIL SERVICE ERRORS
// ============================================================================

export class InvalidVerificationTokenError extends Error {
  constructor(message: string = "Invalid or expired verification token") {
    super(message);
    this.name = "InvalidVerificationTokenError";
  }
}

export class EmailAlreadyVerifiedError extends Error {
  constructor(message: string = "Email is already verified") {
    super(message);
    this.name = "EmailAlreadyVerifiedError";
  }
}

export class EmailSendError extends Error {
  constructor(message: string = "Failed to send email") {
    super(message);
    this.name = "EmailSendError";
  }
}

// ============================================================================
// AUTHENTICATION SERVICE ERRORS
// ============================================================================

export class AuthenticationError extends Error {
  constructor(message: string = "Authentication failed") {
    super(message);
    this.name = "AuthenticationError";
  }
}

export class EmailNotVerifiedError extends Error {
  constructor(message: string = "Email not verified") {
    super(message);
    this.name = "EmailNotVerifiedError";
  }
}

export class InvalidCredentialsError extends Error {
  constructor(message: string = "Invalid credentials") {
    super(message);
    this.name = "InvalidCredentialsError";
  }
}

export class PasswordMismatchError extends Error {
  constructor(message: string = "Current password is incorrect") {
    super(message);
    this.name = "PasswordMismatchError";
  }
}

// ============================================================================
// ANALYTICS SERVICE ERRORS
// ============================================================================

export class InvalidRoleError extends Error {
  constructor(
    message: string = "Invalid role. Must be 'model' or 'advertiser'",
  ) {
    super(message);
    this.name = "InvalidRoleError";
  }
}

export class InsufficientPermissionsError extends Error {
  constructor(message: string = "Insufficient permissions") {
    super(message);
    this.name = "InsufficientPermissionsError";
  }
}

export class AnalyticsDataError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AnalyticsDataError";
  }
}

// ============================================================================
// ADVERTISEMENT SERVICE ERRORS
// ============================================================================

export class AdNotFoundError extends Error {
  constructor(message: string = "Advertisement not found") {
    super(message);
    this.name = "AdNotFoundError";
  }
}

export class NoAdsAvailableError extends Error {
  constructor(message: string = "No advertisements available") {
    super(message);
    this.name = "NoAdsAvailableError";
  }
}

export class AdNotActiveError extends Error {
  constructor(message: string = "Ad or app is not active") {
    super(message);
    this.name = "AdNotActiveError";
  }
}

// ============================================================================
// APPLICATION SERVICE ERRORS
// ============================================================================

export class AppNotFoundError extends Error {
  constructor(message: string = "Application not found") {
    super(message);
    this.name = "AppNotFoundError";
  }
}

export class AppNotActiveError extends Error {
  constructor(message: string = "Application is not active") {
    super(message);
    this.name = "AppNotActiveError";
  }
}

export class InvalidAppCredentialsError extends Error {
  constructor(message: string = "Invalid app credentials") {
    super(message);
    this.name = "InvalidAppCredentialsError";
  }
}

// ============================================================================
// GROUPED EXPORTS FOR CONVENIENCE
// ============================================================================

// User-related errors
export const UserErrors = {
  UserNotFoundError,
  UserAlreadyExistsError,
  EmailAlreadyTakenError,
  InvalidRolesError,
} as const;

// Email-related errors
export const EmailErrors = {
  InvalidVerificationTokenError,
  EmailAlreadyVerifiedError,
  EmailSendError,
} as const;

// Authentication-related errors
export const AuthErrors = {
  AuthenticationError,
  EmailNotVerifiedError,
  InvalidCredentialsError,
  PasswordMismatchError,
} as const;

// Analytics-related errors
export const AnalyticsErrors = {
  InvalidRoleError,
  InsufficientPermissionsError,
  AnalyticsDataError,
} as const;

// Advertisement-related errors
export const AdErrors = {
  AdNotFoundError,
  NoAdsAvailableError,
  AdNotActiveError,
} as const;

// Application-related errors
export const AppErrors = {
  AppNotFoundError,
  AppNotActiveError,
  InvalidAppCredentialsError,
} as const;

// All errors for convenience
export const AllErrors = {
  ...UserErrors,
  ...EmailErrors,
  ...AuthErrors,
  ...AnalyticsErrors,
  ...AdErrors,
  ...AppErrors,
} as const;
