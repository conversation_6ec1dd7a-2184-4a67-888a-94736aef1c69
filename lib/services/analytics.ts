import { prisma } from "@/lib/db";
import {
  getUserAppAnalytics,
  getUserAdAnalytics,
  getMonthlyAppAnalytics,
  getMonthlyAdAnalytics,
  MonthlyData,
} from "@/lib/analytics";

// Analytics service exceptions
export class InvalidRoleError extends Error {
  constructor(
    message: string = "Invalid role. Must be 'model' or 'advertiser'",
  ) {
    super(message);
    this.name = "InvalidRoleError";
  }
}

export class AnalyticsDataError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AnalyticsDataError";
  }
}

export interface ModelAnalytics {
  totalApps: number;
  totalImpressions: number;
  totalClicks: number;
  totalRevenue: number;
  monthlyData: MonthlyData[];
  topApps: Array<{
    id: string;
    name: string;
    createdAt: Date;
    impressions: number;
    clicks: number;
    revenue: number;
  }>;
}

export interface AdvertiserAnalytics {
  totalCampaigns: number;
  totalBudget: number;
  totalSpend: number;
  totalImpressions: number;
  totalClicks: number;
  averageCTR: string;
  monthlyData: MonthlyData[];
  topCampaigns: Array<{
    id: string;
    name: string;
    budget: number;
    bidType: string;
    bidAmount: number;
    createdAt: Date;
    impressions: number;
    clicks: number;
    spend: number;
    ctr: number;
  }>;
}

/**
 * Analytics Service - Business logic layer for analytics operations
 * Handles role-based analytics retrieval for model providers and advertisers
 */
export class AnalyticsService {
  /**
   * Get analytics data for model providers
   */
  static async getModelAnalytics(userId: string): Promise<ModelAnalytics> {
    // Get user's apps
    const apps = await prisma.app.findMany({
      where: { userId },
      select: { id: true, name: true, createdAt: true },
    });

    // Get analytics data
    const userAnalytics = await getUserAppAnalytics(userId);
    const monthlyData = await getMonthlyAppAnalytics(userId);

    // Build analytics response
    const analytics: ModelAnalytics = {
      totalApps: apps.length,
      totalImpressions: userAnalytics.totalImpressions,
      totalClicks: userAnalytics.totalClicks,
      totalRevenue: userAnalytics.totalRevenue,
      monthlyData,
      topApps: userAnalytics.apps.slice(0, 5).map((app) => {
        const found = apps.find((a) => a.id === app.id);

        if (!found) {
          throw new AnalyticsDataError(`App with id ${app.id} not found`);
        }

        return {
          id: app.id,
          name: found.name,
          createdAt: found.createdAt,
          impressions: app.impressions,
          clicks: app.clicks,
          revenue: app.revenue,
        };
      }),
    };

    return analytics;
  }

  /**
   * Get analytics data for advertisers
   */
  static async getAdvertiserAnalytics(
    userId: string,
  ): Promise<AdvertiserAnalytics> {
    // Get user's advertisements
    const ads = await prisma.advertisement.findMany({
      where: { userId },
      select: {
        id: true,
        name: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        createdAt: true,
      },
    });

    // Calculate total budget
    const totalBudget = ads.reduce(
      (sum: number, ad) => sum + Number(ad.budget),
      0,
    );

    // Get analytics data
    const userAnalytics = await getUserAdAnalytics(userId);
    const monthlyData = await getMonthlyAdAnalytics(userId);

    // Build analytics response
    const analytics: AdvertiserAnalytics = {
      totalCampaigns: ads.length,
      totalBudget,
      totalSpend: userAnalytics.totalSpend,
      totalImpressions: userAnalytics.totalImpressions,
      totalClicks: userAnalytics.totalClicks,
      averageCTR: userAnalytics.averageCTR,
      monthlyData,
      topCampaigns: userAnalytics.ads.slice(0, 5).map((ad) => {
        const found = ads.find((a) => a.id === ad.id);

        if (!found) {
          throw new AnalyticsDataError(
            `Advertisement with id ${ad.id} not found`,
          );
        }

        return {
          id: ad.id,
          name: found.name,
          budget: Number(found.budget),
          bidType: found.bidType,
          bidAmount: Number(found.bidAmount),
          createdAt: found.createdAt,
          impressions: ad.impressions,
          clicks: ad.clicks,
          spend: ad.spend ?? 0,
          ctr: parseFloat(ad.ctr),
        };
      }),
    };

    return analytics;
  }
}
