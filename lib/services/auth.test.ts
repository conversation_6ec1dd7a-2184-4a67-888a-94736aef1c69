import crypto from "crypto";

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { Role, TokenType } from "@prisma/client";

import { AuthService } from "./auth";
import { UserService } from "./user";
import { EmailService } from "./email";

import { prisma } from "@/lib/db";
import { hashPassword, verifyPassword } from "@/lib/auth";
import { sanitizeEmail } from "@/lib/validation";
import {
  InvalidRolesError,
  EmailAlreadyVerifiedError,
  InvalidVerificationTokenError,
  AuthenticationError,
  EmailNotVerifiedError,
  InvalidCredentialsError,
  PasswordMismatchError,
} from "@/lib/errors";

describe("AuthService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("registerUser", () => {
    const mockUserData = {
      email: "<EMAIL>",
      password: "password123",
      name: "Test User",
      roles: [Role.ADVERTISER] as Role[],
    };

    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
      image: null,
      roles: [Role.ADVERTISER],
      emailVerified: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully register a new user", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(UserService.createUser).mockResolvedValue(mockUser);
      vi.mocked(EmailService.sendVerificationEmail).mockResolvedValue(
        undefined,
      );

      const result = await AuthService.registerUser(mockUserData);

      expect(sanitizeEmail).toHaveBeenCalledWith("<EMAIL>");
      expect(UserService.createUser).toHaveBeenCalledWith({
        email: "<EMAIL>",
        password: "password123",
        name: "Test User",
        roles: [Role.ADVERTISER],
        emailVerified: false,
      });
      expect(EmailService.sendVerificationEmail).toHaveBeenCalledWith(
        "user-123",
      );
      expect(result).toEqual(mockUser);
    });

    it("should throw InvalidRolesError for empty roles", async () => {
      const invalidData = { ...mockUserData, roles: [] };

      await expect(AuthService.registerUser(invalidData)).rejects.toThrow(
        InvalidRolesError,
      );
      expect(UserService.createUser).not.toHaveBeenCalled();
    });

    it("should throw InvalidRolesError for more than 2 roles", async () => {
      const invalidData = {
        ...mockUserData,
        roles: [Role.ADVERTISER, Role.MODEL_PROVIDER, Role.ADMIN] as Role[],
      };

      await expect(AuthService.registerUser(invalidData)).rejects.toThrow(
        InvalidRolesError,
      );
      expect(UserService.createUser).not.toHaveBeenCalled();
    });

    it("should handle user creation failure", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(UserService.createUser).mockRejectedValue(
        new Error("User creation failed"),
      );

      await expect(AuthService.registerUser(mockUserData)).rejects.toThrow(
        "User creation failed",
      );
      expect(EmailService.sendVerificationEmail).not.toHaveBeenCalled();
    });
  });

  describe("loginUser", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
      image: null,
      roles: [Role.ADVERTISER],
      emailVerified: new Date(),
      passwordHash: "hashed-password",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully login a verified user", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser);
      vi.mocked(verifyPassword).mockResolvedValue(true);

      const result = await AuthService.loginUser(
        "<EMAIL>",
        "password123",
      );

      expect(sanitizeEmail).toHaveBeenCalledWith("<EMAIL>");
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: "<EMAIL>" },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          passwordHash: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(verifyPassword).toHaveBeenCalledWith(
        "password123",
        "hashed-password",
      );
      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        name: mockUser.name,
        image: mockUser.image,
        roles: mockUser.roles,
        emailVerified: mockUser.emailVerified,
        createdAt: mockUser.createdAt,
        updatedAt: mockUser.updatedAt,
      });
    });

    it("should throw InvalidCredentialsError for non-existent user", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      await expect(
        AuthService.loginUser("<EMAIL>", "password123"),
      ).rejects.toThrow(InvalidCredentialsError);
      expect(verifyPassword).not.toHaveBeenCalled();
    });

    it("should throw InvalidCredentialsError for user without password", async () => {
      const userWithoutPassword = { ...mockUser, passwordHash: null };

      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(userWithoutPassword);

      await expect(
        AuthService.loginUser("<EMAIL>", "password123"),
      ).rejects.toThrow(InvalidCredentialsError);
      expect(verifyPassword).not.toHaveBeenCalled();
    });

    it("should throw InvalidCredentialsError for wrong password", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser);
      vi.mocked(verifyPassword).mockResolvedValue(false);

      await expect(
        AuthService.loginUser("<EMAIL>", "wrongpassword"),
      ).rejects.toThrow(InvalidCredentialsError);
    });

    it("should throw EmailNotVerifiedError for unverified user", async () => {
      const unverifiedUser = { ...mockUser, emailVerified: null };

      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(unverifiedUser);
      vi.mocked(verifyPassword).mockResolvedValue(true);

      await expect(
        AuthService.loginUser("<EMAIL>", "password123"),
      ).rejects.toThrow(EmailNotVerifiedError);
    });
  });

  describe("changePassword", () => {
    const mockUser = {
      id: "user-123",
      passwordHash: "current-hashed-password",
    };

    it("should successfully change password", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(verifyPassword).mockResolvedValue(true);
      vi.mocked(hashPassword).mockResolvedValue("new-hashed-password");
      vi.mocked(prisma.user.update).mockResolvedValue({} as any);

      await AuthService.changePassword(
        "user-123",
        "currentPassword",
        "newPassword",
      );

      expect(prisma.user.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "user-123" },
        select: { id: true, passwordHash: true },
      });
      expect(verifyPassword).toHaveBeenCalledWith(
        "currentPassword",
        "current-hashed-password",
      );
      expect(hashPassword).toHaveBeenCalledWith("newPassword");
      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-123" },
        data: { passwordHash: "new-hashed-password" },
      });
    });

    it("should throw PasswordMismatchError for wrong current password", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(verifyPassword).mockResolvedValue(false);

      await expect(
        AuthService.changePassword("user-123", "wrongPassword", "newPassword"),
      ).rejects.toThrow(PasswordMismatchError);
      expect(hashPassword).not.toHaveBeenCalled();
      expect(prisma.user.update).not.toHaveBeenCalled();
    });

    it("should throw AuthenticationError for user without password", async () => {
      const userWithoutPassword = { id: "user-123", passwordHash: null };

      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(
        userWithoutPassword,
      );

      await expect(
        AuthService.changePassword(
          "user-123",
          "currentPassword",
          "newPassword",
        ),
      ).rejects.toThrow(AuthenticationError);
      expect(verifyPassword).not.toHaveBeenCalled();
    });
  });

  describe("verifyToken", () => {
    const mockToken = "verification-token";
    const mockTokenHash = "hashed-token";
    const mockUserToken = {
      id: "token-123",
      userId: "user-123",
      type: TokenType.EMAIL_VERIFICATION,
      token: mockTokenHash,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      user: {
        id: "user-123",
        emailVerified: null,
      },
    };

    beforeEach(() => {
      const mockCrypto = vi.mocked(crypto.createHash);

      mockCrypto.mockReturnValue({
        update: vi.fn().mockReturnValue({
          digest: vi.fn().mockReturnValue(mockTokenHash),
        }),
      } as any);
    });

    it("should successfully verify email token", async () => {
      vi.mocked(prisma.userToken.findFirst).mockResolvedValue(mockUserToken);
      vi.mocked(prisma.$transaction).mockImplementation(async (callback) => {
        return await callback({
          user: {
            update: vi.fn().mockResolvedValue({}),
          },
          userToken: {
            delete: vi.fn().mockResolvedValue({}),
          },
        } as any);
      });

      await AuthService.verifyToken(mockToken);

      expect(crypto.createHash).toHaveBeenCalledWith("sha256");
      expect(prisma.userToken.findFirst).toHaveBeenCalledWith({
        where: {
          token: mockTokenHash,
          type: TokenType.EMAIL_VERIFICATION,
          expiresAt: { gt: expect.any(Date) },
        },
        include: {
          user: {
            select: { id: true, emailVerified: true },
          },
        },
      });
      expect(prisma.$transaction).toHaveBeenCalled();
    });

    it("should throw InvalidVerificationTokenError for non-existent token", async () => {
      vi.mocked(prisma.userToken.findFirst).mockResolvedValue(null);

      await expect(AuthService.verifyToken(mockToken)).rejects.toThrow(
        InvalidVerificationTokenError,
      );
      expect(prisma.user.update).not.toHaveBeenCalled();
      expect(prisma.userToken.delete).not.toHaveBeenCalled();
    });

    it("should throw EmailAlreadyVerifiedError for already verified user", async () => {
      const verifiedUserToken = {
        ...mockUserToken,
        user: {
          id: "user-123",
          emailVerified: new Date(),
        },
      };

      vi.mocked(prisma.userToken.findFirst).mockResolvedValue(
        verifiedUserToken,
      );

      await expect(AuthService.verifyToken(mockToken)).rejects.toThrow(
        EmailAlreadyVerifiedError,
      );
      expect(prisma.user.update).not.toHaveBeenCalled();
      expect(prisma.userToken.delete).not.toHaveBeenCalled();
    });

    it("should handle database errors gracefully", async () => {
      vi.mocked(prisma.userToken.findFirst).mockRejectedValue(
        new Error("Database error"),
      );

      await expect(AuthService.verifyToken(mockToken)).rejects.toThrow(
        "Database error",
      );
    });
  });
});
