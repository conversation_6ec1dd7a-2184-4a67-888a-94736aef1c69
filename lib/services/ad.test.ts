import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { AdStatus, BidType, AppStatus } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";

import { AdService } from "./ad";

import { prisma } from "@/lib/db";
import { getAdAnalytics } from "@/lib/analytics";
import { AuthenticationError, AdNotActiveError } from "@/lib/errors";

describe("AdService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("createAd", () => {
    const mockCreateAdData = {
      userId: "user-123",
      name: "Test Ad",
      description: "Test Description",
      productUrl: "https://example.com",
      imageUrl: "https://example.com/image.jpg",
      targetTopics: ["tech", "ai"],
      budget: 1000,
      bidType: BidType.CPC,
      bidAmount: 0.5,
    };

    const mockUser = {
      id: "user-123",
      roles: ["ADVERTISER"],
    };

    const mockCreatedAd = {
      id: "ad-123",
      userId: "user-123",
      name: "Test Ad",
      description: "Test Description",
      productUrl: "https://example.com",
      imageUrl: "https://example.com/image.jpg",
      targetTopics: ["tech", "ai"],
      budget: new Decimal(1000),
      bidType: BidType.CPC,
      bidAmount: new Decimal(0.5),
      status: AdStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      impressions: 0,
      clicks: 0,
      spend: 0,
      ctr: "0.00",
    };

    it("should successfully create an ad", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(prisma.advertisement.create).mockResolvedValue(mockCreatedAd);
      vi.mocked(getAdAnalytics).mockResolvedValue({
        impressions: 0,
        clicks: 0,
        spend: 0,
        ctr: "0.00",
      });

      const result = await AdService.createAd(mockCreateAdData);

      expect(prisma.user.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "user-123" },
        select: { id: true, roles: true },
      });
      expect(prisma.advertisement.create).toHaveBeenCalledWith({
        data: {
          userId: "user-123",
          name: "Test Ad",
          description: "Test Description",
          productUrl: "https://example.com",
          imageUrl: "https://example.com/image.jpg",
          targetTopics: ["tech", "ai"],
          budget: 1000,
          bidType: BidType.CPC,
          bidAmount: 0.5,
          status: AdStatus.ACTIVE,
        },
        select: {
          id: true,
          userId: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(getAdAnalytics).toHaveBeenCalledWith("ad-123");
      expect(result).toEqual(mockCreatedAd);
    });

    it("should throw AuthenticationError for non-advertiser user", async () => {
      const nonAdvertiserUser = { id: "user-123", roles: ["MODEL_PROVIDER"] };

      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(
        nonAdvertiserUser,
      );

      await expect(AdService.createAd(mockCreateAdData)).rejects.toThrow(
        AuthenticationError,
      );
      expect(prisma.advertisement.create).not.toHaveBeenCalled();
    });

    it("should handle database errors", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(prisma.advertisement.create).mockRejectedValue(
        new Error("Database error"),
      );

      await expect(AdService.createAd(mockCreateAdData)).rejects.toThrow(
        "Database error",
      );
    });
  });

  describe("getUserAds", () => {
    const mockAds = [
      {
        id: "ad-1",
        userId: "user-123",
        name: "Ad 1",
        description: "Test ad 1",
        productUrl: "https://example.com/1",
        imageUrl: "https://example.com/image1.jpg",
        targetTopics: ["tech"],
        budget: new Decimal(100),
        bidType: BidType.CPC,
        bidAmount: new Decimal(0.5),
        status: AdStatus.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "ad-2",
        userId: "user-123",
        name: "Ad 2",
        description: "Test ad 2",
        productUrl: "https://example.com/2",
        imageUrl: "https://example.com/image2.jpg",
        targetTopics: ["ai"],
        budget: new Decimal(200),
        bidType: BidType.CPM,
        bidAmount: new Decimal(2.0),
        status: AdStatus.PAUSED,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    it("should successfully get ads by user ID", async () => {
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds);
      vi.mocked(getAdAnalytics)
        .mockResolvedValueOnce({
          impressions: 100,
          clicks: 5,
          spend: 25,
          ctr: "5.00",
        })
        .mockResolvedValueOnce({
          impressions: 50,
          clicks: 2,
          spend: 10,
          ctr: "4.00",
        });

      const result = await AdService.getUserAds("user-123");

      expect(prisma.advertisement.findMany).toHaveBeenCalledWith({
        where: { userId: "user-123" },
        select: {
          id: true,
          userId: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { createdAt: "desc" },
      });
      expect(result).toEqual(mockAds);
    });

    it("should return empty array when no ads found", async () => {
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue([]);

      const result = await AdService.getUserAds("user-123");

      expect(result).toEqual([]);
    });
  });

  describe("getAdById", () => {
    const mockAd = {
      id: "ad-123",
      userId: "user-123",
      name: "Test Ad",
      description: "Test Description",
      productUrl: "https://example.com",
      imageUrl: "https://example.com/image.jpg",
      targetTopics: ["tech"],
      budget: new Decimal(100),
      bidType: BidType.CPC,
      bidAmount: new Decimal(0.5),
      status: AdStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully get ad by ID with analytics", async () => {
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(
        mockAd,
      );
      vi.mocked(getAdAnalytics).mockResolvedValue({
        impressions: 100,
        clicks: 5,
        spend: 25,
        ctr: "5.00",
      });

      const result = await AdService.getAdById("ad-123");

      expect(prisma.advertisement.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "ad-123" },
        select: {
          id: true,
          userId: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(getAdAnalytics).toHaveBeenCalledWith("ad-123");
      expect(result).toEqual({
        ...mockAd,
        impressions: 100,
        clicks: 5,
        spend: 25,
        ctr: "5.00",
      });
    });

    it("should handle ad not found", async () => {
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockRejectedValue(
        new Error("Ad not found"),
      );

      await expect(AdService.getAdById("non-existent")).rejects.toThrow(
        "Ad not found",
      );
    });
  });

  describe("updateAd", () => {
    const mockUpdateData = {
      name: "Updated Ad",
      description: "Updated Description",
      status: AdStatus.PAUSED,
    };

    const mockUpdatedAd = {
      id: "ad-123",
      userId: "user-123",
      name: "Updated Ad",
      description: "Updated Description",
      productUrl: "https://example.com",
      imageUrl: "https://example.com/image.jpg",
      targetTopics: ["tech"],
      budget: new Decimal(100),
      bidType: BidType.CPC,
      bidAmount: new Decimal(0.5),
      status: AdStatus.PAUSED,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully update an ad", async () => {
      vi.mocked(prisma.advertisement.update).mockResolvedValue(mockUpdatedAd);
      vi.mocked(getAdAnalytics).mockResolvedValue({
        impressions: 50,
        clicks: 2,
        spend: 10,
        ctr: "4.00",
      });

      const result = await AdService.updateAd(
        "ad-123",
        mockUpdateData,
        "user-123",
      );

      expect(prisma.advertisement.update).toHaveBeenCalledWith({
        where: { id: "ad-123", userId: "user-123" },
        data: mockUpdateData,
        select: {
          id: true,
          userId: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(getAdAnalytics).toHaveBeenCalledWith("ad-123");
      expect(result).toEqual({
        ...mockUpdatedAd,
        impressions: 50,
        clicks: 2,
        spend: 10,
        ctr: "4.00",
      });
    });

    it("should handle update failure", async () => {
      vi.mocked(prisma.advertisement.update).mockRejectedValue(
        new Error("Update failed"),
      );

      await expect(
        AdService.updateAd("ad-123", mockUpdateData, "user-123"),
      ).rejects.toThrow("Update failed");
    });
  });

  describe("deleteAd", () => {
    it("should successfully delete an ad", async () => {
      vi.mocked(prisma.advertisement.delete).mockResolvedValue({} as any);

      await AdService.deleteAd("ad-123", "user-123");

      expect(prisma.advertisement.delete).toHaveBeenCalledWith({
        where: { id: "ad-123", userId: "user-123" },
      });
    });

    it("should handle delete failure", async () => {
      vi.mocked(prisma.advertisement.delete).mockRejectedValue(
        new Error("Delete failed"),
      );

      await expect(AdService.deleteAd("ad-123", "user-123")).rejects.toThrow(
        "Delete failed",
      );
    });
  });

  describe("recordImpression", () => {
    const mockAd = {
      id: "ad-123",
      status: AdStatus.ACTIVE,
      name: "Test Ad",
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: "user-123",
      description: "Test Description",
      imageUrl: "https://example.com/image.jpg",
      productUrl: "https://example.com",
      targetTopics: ["tech"],
      budget: new Decimal(100),
      bidType: BidType.CPC,
      bidAmount: new Decimal(0.5),
    };

    const mockApp = {
      id: "app-123",
      status: AppStatus.ACTIVE,
      name: "Test App",
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: "user-123",
      appId: "app_123",
      appSecret: "secret_123",
      description: "Test App Description",
    };

    it("should successfully record impression", async () => {
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(
        mockAd,
      );
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);
      vi.mocked(prisma.adImpression.create).mockResolvedValue({} as any);

      await AdService.recordImpression(
        "ad-123",
        "app-123",
        false,
        "***********",
        "Mozilla/5.0",
      );

      expect(prisma.advertisement.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "ad-123" },
        select: { id: true, status: true },
      });
      expect(prisma.app.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "app-123" },
        select: { id: true, status: true },
      });
      expect(prisma.adImpression.create).toHaveBeenCalledWith({
        data: {
          adId: "ad-123",
          appId: "app-123",
          clicked: false,
          ipAddress: "***********",
          userAgent: "Mozilla/5.0",
          timestamp: expect.any(Date),
        },
      });
    });

    it("should record click when clicked is true", async () => {
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(
        mockAd,
      );
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);
      vi.mocked(prisma.adImpression.create).mockResolvedValue({} as any);

      await AdService.recordImpression(
        "ad-123",
        "app-123",
        true,
        "***********",
        "Mozilla/5.0",
      );

      expect(prisma.adImpression.create).toHaveBeenCalledWith({
        data: {
          adId: "ad-123",
          appId: "app-123",
          clicked: true,
          ipAddress: "***********",
          userAgent: "Mozilla/5.0",
          timestamp: expect.any(Date),
        },
      });
    });

    it("should throw AdNotActiveError for inactive ad", async () => {
      const inactiveAd = { ...mockAd, status: AdStatus.PAUSED };

      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(
        inactiveAd,
      );
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);

      await expect(
        AdService.recordImpression(
          "ad-123",
          "app-123",
          false,
          "***********",
          "Mozilla/5.0",
        ),
      ).rejects.toThrow(AdNotActiveError);
      expect(prisma.adImpression.create).not.toHaveBeenCalled();
    });

    it("should throw AdNotActiveError for inactive app", async () => {
      const inactiveApp = { ...mockApp, status: AppStatus.SUSPENDED };

      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(
        mockAd,
      );
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(inactiveApp);

      await expect(
        AdService.recordImpression(
          "ad-123",
          "app-123",
          false,
          "***********",
          "Mozilla/5.0",
        ),
      ).rejects.toThrow(AdNotActiveError);
      expect(prisma.adImpression.create).not.toHaveBeenCalled();
    });
  });
});
