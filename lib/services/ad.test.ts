import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { AdStatus, BidType } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";

import { AdService } from "./ad";
import { prisma } from "@/lib/db";
import { getAdAnalytics } from "@/lib/analytics";
import {
  AuthenticationError,
  InvalidAppCredentialsError,
  NoAdsAvailableError,
  AdNotActiveError,
} from "@/lib/errors";

// Mock dependencies
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUniqueOrThrow: vi.fn(),
    },
    advertisement: {
      create: vi.fn(),
      findMany: vi.fn(),
      findUniqueOrThrow: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    app: {
      findUniqueOrThrow: vi.fn(),
    },
    adImpression: {
      create: vi.fn(),
    },
  },
}));

vi.mock("@/lib/analytics", () => ({
  getAdAnalytics: vi.fn(),
}));

describe("AdService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("createAd", () => {
    const mockCreateAdData = {
      userId: "user-123",
      name: "Test Ad",
      description: "Test Description",
      productUrl: "https://example.com",
      imageUrl: "https://example.com/image.jpg",
      targetTopics: ["tech", "ai"],
      budget: 1000,
      bidType: BidType.CPC,
      bidAmount: 0.5,
    };

    const mockUser = {
      id: "user-123",
      roles: ["ADVERTISER"],
    };

    const mockCreatedAd = {
      id: "ad-123",
      userId: "user-123",
      name: "Test Ad",
      description: "Test Description",
      productUrl: "https://example.com",
      imageUrl: "https://example.com/image.jpg",
      targetTopics: ["tech", "ai"],
      budget: new Decimal(1000),
      bidType: BidType.CPC,
      bidAmount: new Decimal(0.5),
      status: AdStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      impressions: 0,
      clicks: 0,
      spend: 0,
      ctr: "0.00",
    };

    it("should successfully create an ad", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(prisma.advertisement.create).mockResolvedValue(mockCreatedAd);
      vi.mocked(getAdAnalytics).mockResolvedValue({
        impressions: 0,
        clicks: 0,
        spend: 0,
        ctr: "0.00",
      });

      const result = await AdService.createAd(mockCreateAdData);

      expect(prisma.user.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "user-123" },
        select: { id: true, roles: true },
      });
      expect(prisma.advertisement.create).toHaveBeenCalledWith({
        data: {
          userId: "user-123",
          name: "Test Ad",
          description: "Test Description",
          productUrl: "https://example.com",
          imageUrl: "https://example.com/image.jpg",
          targetTopics: ["tech", "ai"],
          budget: 1000,
          bidType: BidType.CPC,
          bidAmount: 0.5,
          status: AdStatus.ACTIVE,
        },
        select: {
          id: true,
          userId: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(getAdAnalytics).toHaveBeenCalledWith("ad-123");
      expect(result).toEqual(mockCreatedAd);
    });

    it("should throw AuthenticationError for non-advertiser user", async () => {
      const nonAdvertiserUser = { id: "user-123", roles: ["MODEL_PROVIDER"] };
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(nonAdvertiserUser);

      await expect(AdService.createAd(mockCreateAdData)).rejects.toThrow(
        AuthenticationError
      );
      expect(prisma.advertisement.create).not.toHaveBeenCalled();
    });

    it("should handle database errors", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(prisma.advertisement.create).mockRejectedValue(
        new Error("Database error")
      );

      await expect(AdService.createAd(mockCreateAdData)).rejects.toThrow(
        "Database error"
      );
    });
  });

  describe("getAdsByUserId", () => {
    const mockAds = [
      {
        id: "ad-1",
        name: "Ad 1",
        status: AdStatus.ACTIVE,
        createdAt: new Date(),
      },
      {
        id: "ad-2",
        name: "Ad 2",
        status: AdStatus.PAUSED,
        createdAt: new Date(),
      },
    ];

    it("should successfully get ads by user ID", async () => {
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds);

      const result = await AdService.getAdsByUserId("user-123");

      expect(prisma.advertisement.findMany).toHaveBeenCalledWith({
        where: { userId: "user-123" },
        select: {
          id: true,
          userId: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { createdAt: "desc" },
      });
      expect(result).toEqual(mockAds);
    });

    it("should return empty array when no ads found", async () => {
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue([]);

      const result = await AdService.getAdsByUserId("user-123");

      expect(result).toEqual([]);
    });
  });

  describe("getAdById", () => {
    const mockAd = {
      id: "ad-123",
      name: "Test Ad",
      status: AdStatus.ACTIVE,
      createdAt: new Date(),
      impressions: 100,
      clicks: 5,
      spend: 25,
      ctr: "5.00",
    };

    it("should successfully get ad by ID with analytics", async () => {
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(mockAd);
      vi.mocked(getAdAnalytics).mockResolvedValue({
        impressions: 100,
        clicks: 5,
        spend: 25,
        ctr: "5.00",
      });

      const result = await AdService.getAdById("ad-123");

      expect(prisma.advertisement.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "ad-123" },
        select: {
          id: true,
          userId: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(getAdAnalytics).toHaveBeenCalledWith("ad-123");
      expect(result).toEqual(mockAd);
    });

    it("should handle ad not found", async () => {
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockRejectedValue(
        new Error("Ad not found")
      );

      await expect(AdService.getAdById("non-existent")).rejects.toThrow(
        "Ad not found"
      );
    });
  });

  describe("updateAd", () => {
    const mockUpdateData = {
      name: "Updated Ad",
      description: "Updated Description",
      status: AdStatus.PAUSED,
    };

    const mockUpdatedAd = {
      id: "ad-123",
      name: "Updated Ad",
      description: "Updated Description",
      status: AdStatus.PAUSED,
      createdAt: new Date(),
      updatedAt: new Date(),
      impressions: 50,
      clicks: 2,
      spend: 10,
      ctr: "4.00",
    };

    it("should successfully update an ad", async () => {
      vi.mocked(prisma.advertisement.update).mockResolvedValue(mockUpdatedAd);
      vi.mocked(getAdAnalytics).mockResolvedValue({
        impressions: 50,
        clicks: 2,
        spend: 10,
        ctr: "4.00",
      });

      const result = await AdService.updateAd("ad-123", "user-123", mockUpdateData);

      expect(prisma.advertisement.update).toHaveBeenCalledWith({
        where: { id: "ad-123", userId: "user-123" },
        data: mockUpdateData,
        select: {
          id: true,
          userId: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(getAdAnalytics).toHaveBeenCalledWith("ad-123");
      expect(result).toEqual(mockUpdatedAd);
    });

    it("should handle update failure", async () => {
      vi.mocked(prisma.advertisement.update).mockRejectedValue(
        new Error("Update failed")
      );

      await expect(
        AdService.updateAd("ad-123", "user-123", mockUpdateData)
      ).rejects.toThrow("Update failed");
    });
  });

  describe("deleteAd", () => {
    it("should successfully delete an ad", async () => {
      vi.mocked(prisma.advertisement.delete).mockResolvedValue({} as any);

      await AdService.deleteAd("ad-123", "user-123");

      expect(prisma.advertisement.delete).toHaveBeenCalledWith({
        where: { id: "ad-123", userId: "user-123" },
      });
    });

    it("should handle delete failure", async () => {
      vi.mocked(prisma.advertisement.delete).mockRejectedValue(
        new Error("Delete failed")
      );

      await expect(AdService.deleteAd("ad-123", "user-123")).rejects.toThrow(
        "Delete failed"
      );
    });
  });

  describe("getMatchingAds", () => {
    const mockApp = {
      id: "app-123",
      appSecret: "secret-123",
      status: "ACTIVE",
    };

    const mockAds = [
      {
        id: "ad-1",
        name: "Ad 1",
        description: "Tech ad",
        productUrl: "https://example.com/1",
        imageUrl: "https://example.com/image1.jpg",
        targetTopics: ["tech"],
        bidType: BidType.CPC,
        bidAmount: new Decimal(0.5),
      },
      {
        id: "ad-2",
        name: "Ad 2",
        description: "AI ad",
        productUrl: "https://example.com/2",
        imageUrl: "https://example.com/image2.jpg",
        targetTopics: ["ai"],
        bidType: BidType.CPM,
        bidAmount: new Decimal(2.0),
      },
    ];

    it("should successfully get matching ads", async () => {
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds);

      const result = await AdService.getMatchingAds(
        "app-123",
        "secret-123",
        ["tech", "ai"]
      );

      expect(prisma.app.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { appId: "app-123" },
        select: {
          id: true,
          appSecret: true,
          status: true,
          name: true,
        },
      });
      expect(prisma.advertisement.findMany).toHaveBeenCalledWith({
        where: {
          status: AdStatus.ACTIVE,
          targetTopics: {
            hasSome: ["tech", "ai"],
          },
        },
        select: {
          id: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          bidType: true,
          bidAmount: true,
        },
      });
      expect(result).toHaveLength(1); // Should return one weighted ad
      expect(result[0]).toHaveProperty("id");
      expect(result[0]).toHaveProperty("name");
      expect(result[0]).toHaveProperty("weight");
    });

    it("should throw InvalidAppCredentialsError for wrong credentials", async () => {
      const invalidApp = { ...mockApp, appSecret: "wrong-secret" };
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(invalidApp);

      await expect(
        AdService.getMatchingAds("app-123", "wrong-secret", ["tech"])
      ).rejects.toThrow(InvalidAppCredentialsError);
      expect(prisma.advertisement.findMany).not.toHaveBeenCalled();
    });

    it("should throw InvalidAppCredentialsError for inactive app", async () => {
      const inactiveApp = { ...mockApp, status: "INACTIVE" };
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(inactiveApp);

      await expect(
        AdService.getMatchingAds("app-123", "secret-123", ["tech"])
      ).rejects.toThrow(InvalidAppCredentialsError);
    });

    it("should throw NoAdsAvailableError when no ads found", async () => {
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue([]);

      await expect(
        AdService.getMatchingAds("app-123", "secret-123", ["tech"])
      ).rejects.toThrow(NoAdsAvailableError);
    });

    it("should get all ads when no topics provided", async () => {
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds);

      await AdService.getMatchingAds("app-123", "secret-123");

      expect(prisma.advertisement.findMany).toHaveBeenCalledWith({
        where: {
          status: AdStatus.ACTIVE,
        },
        select: {
          id: true,
          name: true,
          description: true,
          productUrl: true,
          imageUrl: true,
          targetTopics: true,
          bidType: true,
          bidAmount: true,
        },
      });
    });
  });

  describe("recordImpression", () => {
    const mockAd = { id: "ad-123", status: AdStatus.ACTIVE };
    const mockApp = { id: "app-123", status: "ACTIVE" };

    it("should successfully record impression", async () => {
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(mockAd);
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);
      vi.mocked(prisma.adImpression.create).mockResolvedValue({} as any);

      await AdService.recordImpression("ad-123", "app-123", false);

      expect(prisma.advertisement.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "ad-123" },
        select: { id: true, status: true },
      });
      expect(prisma.app.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "app-123" },
        select: { id: true, status: true },
      });
      expect(prisma.adImpression.create).toHaveBeenCalledWith({
        data: {
          adId: "ad-123",
          appId: "app-123",
          clicked: false,
          timestamp: expect.any(Date),
        },
      });
    });

    it("should record click when clicked is true", async () => {
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(mockAd);
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);
      vi.mocked(prisma.adImpression.create).mockResolvedValue({} as any);

      await AdService.recordImpression("ad-123", "app-123", true);

      expect(prisma.adImpression.create).toHaveBeenCalledWith({
        data: {
          adId: "ad-123",
          appId: "app-123",
          clicked: true,
          timestamp: expect.any(Date),
        },
      });
    });

    it("should throw AdNotActiveError for inactive ad", async () => {
      const inactiveAd = { id: "ad-123", status: AdStatus.PAUSED };
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(inactiveAd);
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);

      await expect(
        AdService.recordImpression("ad-123", "app-123", false)
      ).rejects.toThrow(AdNotActiveError);
      expect(prisma.adImpression.create).not.toHaveBeenCalled();
    });

    it("should throw AdNotActiveError for inactive app", async () => {
      const inactiveApp = { id: "app-123", status: "INACTIVE" };
      vi.mocked(prisma.advertisement.findUniqueOrThrow).mockResolvedValue(mockAd);
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(inactiveApp);

      await expect(
        AdService.recordImpression("ad-123", "app-123", false)
      ).rejects.toThrow(AdNotActiveError);
      expect(prisma.adImpression.create).not.toHaveBeenCalled();
    });
  });
});
