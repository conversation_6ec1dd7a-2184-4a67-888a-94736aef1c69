import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { Role } from "@prisma/client";

import { UserService } from "./user";
import { prisma } from "@/lib/db";
import { hashPassword } from "@/lib/auth";
import { sanitizeEmail } from "@/lib/validation";
import {
  UserAlreadyExistsError,
  EmailAlreadyTakenError,
  InvalidRolesError,
} from "@/lib/errors";

// Mock dependencies
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      findUniqueOrThrow: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
  },
}));

vi.mock("@/lib/auth", () => ({
  hashPassword: vi.fn(),
}));

vi.mock("@/lib/validation", () => ({
  sanitizeEmail: vi.fn(),
}));

describe("UserService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("createUser", () => {
    const mockUserData = {
      email: "<EMAIL>",
      password: "password123",
      name: "Test User",
      roles: [Role.ADVERTISER] as Role[],
      emailVerified: false,
    };

    const mockCreatedUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
      image: null,
      roles: [Role.ADVERTISER],
      emailVerified: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully create a new user with password", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
      vi.mocked(hashPassword).mockResolvedValue("hashed-password");
      vi.mocked(prisma.user.create).mockResolvedValue(mockCreatedUser);

      const result = await UserService.createUser(mockUserData);

      expect(sanitizeEmail).toHaveBeenCalledWith("<EMAIL>");
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: "<EMAIL>" },
      });
      expect(hashPassword).toHaveBeenCalledWith("password123");
      expect(prisma.user.create).toHaveBeenCalledWith({
        data: {
          email: "<EMAIL>",
          passwordHash: "hashed-password",
          name: "Test User",
          roles: [Role.ADVERTISER],
          emailVerified: null,
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result).toEqual(mockCreatedUser);
    });

    it("should successfully create a user without password", async () => {
      const userDataWithoutPassword = { ...mockUserData, password: undefined };
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
      vi.mocked(prisma.user.create).mockResolvedValue(mockCreatedUser);

      const result = await UserService.createUser(userDataWithoutPassword);

      expect(hashPassword).not.toHaveBeenCalled();
      expect(prisma.user.create).toHaveBeenCalledWith({
        data: {
          email: "<EMAIL>",
          passwordHash: undefined,
          name: "Test User",
          roles: [Role.ADVERTISER],
          emailVerified: null,
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result).toEqual(mockCreatedUser);
    });

    it("should create user with verified email when emailVerified is true", async () => {
      const verifiedUserData = { ...mockUserData, emailVerified: true };
      const verifiedUser = { ...mockCreatedUser, emailVerified: new Date() };
      
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
      vi.mocked(hashPassword).mockResolvedValue("hashed-password");
      vi.mocked(prisma.user.create).mockResolvedValue(verifiedUser);

      const result = await UserService.createUser(verifiedUserData);

      expect(prisma.user.create).toHaveBeenCalledWith({
        data: {
          email: "<EMAIL>",
          passwordHash: "hashed-password",
          name: "Test User",
          roles: [Role.ADVERTISER],
          emailVerified: expect.any(Date),
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result).toEqual(verifiedUser);
    });

    it("should throw UserAlreadyExistsError when user exists", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockCreatedUser);

      await expect(UserService.createUser(mockUserData)).rejects.toThrow(
        UserAlreadyExistsError
      );
      expect(hashPassword).not.toHaveBeenCalled();
      expect(prisma.user.create).not.toHaveBeenCalled();
    });

    it("should handle database errors", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
      vi.mocked(hashPassword).mockResolvedValue("hashed-password");
      vi.mocked(prisma.user.create).mockRejectedValue(new Error("Database error"));

      await expect(UserService.createUser(mockUserData)).rejects.toThrow(
        "Database error"
      );
    });
  });

  describe("getUserById", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
      image: null,
      roles: [Role.ADVERTISER],
      emailVerified: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully get user by ID", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);

      const result = await UserService.getUserById("user-123");

      expect(prisma.user.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "user-123" },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result).toEqual(mockUser);
    });

    it("should throw error when user not found", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockRejectedValue(
        new Error("User not found")
      );

      await expect(UserService.getUserById("non-existent")).rejects.toThrow(
        "User not found"
      );
    });
  });

  describe("getUserByEmail", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
      image: null,
      roles: [Role.ADVERTISER],
      emailVerified: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully get user by email", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);

      const result = await UserService.getUserByEmail("<EMAIL>");

      expect(sanitizeEmail).toHaveBeenCalledWith("<EMAIL>");
      expect(prisma.user.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { email: "<EMAIL>" },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result).toEqual(mockUser);
    });

    it("should throw error when user not found", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findUniqueOrThrow).mockRejectedValue(
        new Error("User not found")
      );

      await expect(UserService.getUserByEmail("<EMAIL>")).rejects.toThrow(
        "User not found"
      );
    });
  });

  describe("updateUser", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Updated User",
      image: "new-image.jpg",
      roles: [Role.MODEL_PROVIDER],
      emailVerified: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully update user name and image", async () => {
      vi.mocked(prisma.user.update).mockResolvedValue(mockUser);

      const result = await UserService.updateUser("user-123", {
        name: "Updated User",
        image: "new-image.jpg",
      });

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-123" },
        data: {
          name: "Updated User",
          image: "new-image.jpg",
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result).toEqual(mockUser);
    });

    it("should successfully update user roles", async () => {
      vi.mocked(prisma.user.update).mockResolvedValue(mockUser);

      const result = await UserService.updateUser("user-123", {
        roles: [Role.MODEL_PROVIDER],
      });

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-123" },
        data: {
          roles: [Role.MODEL_PROVIDER],
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result).toEqual(mockUser);
    });

    it("should successfully update user email and reset verification", async () => {
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findFirst).mockResolvedValue(null);
      vi.mocked(prisma.user.update).mockResolvedValue(mockUser);

      const result = await UserService.updateUser("user-123", {
        email: "<EMAIL>",
      });

      expect(sanitizeEmail).toHaveBeenCalledWith("<EMAIL>");
      expect(prisma.user.findFirst).toHaveBeenCalledWith({
        where: {
          email: "<EMAIL>",
          NOT: { id: "user-123" },
        },
      });
      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-123" },
        data: {
          email: "<EMAIL>",
          emailVerified: null,
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result).toEqual(mockUser);
    });

    it("should throw InvalidRolesError for empty roles", async () => {
      await expect(
        UserService.updateUser("user-123", { roles: [] })
      ).rejects.toThrow(InvalidRolesError);
      expect(prisma.user.update).not.toHaveBeenCalled();
    });

    it("should throw InvalidRolesError for more than 2 roles", async () => {
      await expect(
        UserService.updateUser("user-123", {
          roles: [Role.ADVERTISER, Role.MODEL_PROVIDER, Role.ADMIN],
        })
      ).rejects.toThrow(InvalidRolesError);
      expect(prisma.user.update).not.toHaveBeenCalled();
    });

    it("should throw EmailAlreadyTakenError when email is taken", async () => {
      const existingUser = { id: "other-user", email: "<EMAIL>" };
      vi.mocked(sanitizeEmail).mockReturnValue("<EMAIL>");
      vi.mocked(prisma.user.findFirst).mockResolvedValue(existingUser);

      await expect(
        UserService.updateUser("user-123", { email: "<EMAIL>" })
      ).rejects.toThrow(EmailAlreadyTakenError);
      expect(prisma.user.update).not.toHaveBeenCalled();
    });

    it("should filter out invalid roles", async () => {
      vi.mocked(prisma.user.update).mockResolvedValue(mockUser);

      await UserService.updateUser("user-123", {
        roles: [Role.ADVERTISER, "INVALID_ROLE" as Role],
      });

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-123" },
        data: {
          roles: [Role.ADVERTISER],
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    });
  });
});
