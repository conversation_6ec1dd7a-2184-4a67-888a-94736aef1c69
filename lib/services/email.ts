import crypto from "crypto";

import { TokenType } from "@prisma/client";

import { prisma } from "@/lib/db";
import {
  sendEmail,
  generateVerificationToken,
  getVerificationExpiry,
  EmailData,
} from "@/lib/email";
import {
  getEmailVerificationTemplate,
  getWelcomeEmailTemplate,
} from "@/lib/email-templates";

// Custom email service exceptions
export class InvalidVerificationTokenError extends Error {
  constructor(message: string = "Invalid or expired verification token") {
    super(message);
    this.name = "InvalidVerificationTokenError";
  }
}

export class EmailAlreadyVerifiedError extends Error {
  constructor(message: string = "Email is already verified") {
    super(message);
    this.name = "EmailAlreadyVerifiedError";
  }
}

export class EmailSendError extends Error {
  constructor(message: string = "Failed to send email") {
    super(message);
    this.name = "EmailSendError";
  }
}

/**
 * Email Service - Business logic layer for email operations
 * Handles email verification, welcome emails, and general email sending
 */
export class EmailService {
  /**
   * Send a generic email
   */
  static async sendGenericEmail(emailData: EmailData): Promise<{
    provider: string;
    sentDate: Date;
  }> {
    const result = await sendEmail(emailData);

    if (!result.success) {
      throw new EmailSendError(result.error || "Failed to send email");
    }

    return { provider: result.provider, sentDate: new Date() };
  }

  /**
   * Create and store email verification token for a user
   */
  static async sendVerificationEmail(
    userId: string,
    name?: string,
  ): Promise<{
    token: string;
    expires: Date;
  }> {
    // Get user info for email
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
      },
    });
    // Generate verification token and expiry
    const verificationToken = generateVerificationToken();
    const verificationExpires = getVerificationExpiry();

    // Hash the token for storage
    const tokenHash = crypto
      .createHash("sha256")
      .update(verificationToken)
      .digest("hex");

    // Delete any existing email verification tokens for this user
    await prisma.userToken.deleteMany({
      where: {
        userId,
        type: TokenType.EMAIL_VERIFICATION,
      },
    });

    // Create new verification token
    await prisma.userToken.create({
      data: {
        userId,
        tokenHash,
        type: TokenType.EMAIL_VERIFICATION,
        expiresAt: verificationExpires,
      },
    });

    // Send verification email
    const mailContent = getEmailVerificationTemplate({
      email: user.email,
      token: verificationToken,
      name: name || user.name || undefined,
    });

    await EmailService.sendGenericEmail({
      to: user.email,
      subject: mailContent.subject,
      html: mailContent.html,
      text: mailContent.text,
    });

    return {
      token: verificationToken,
      expires: verificationExpires,
    };
  }

  /**
   * Send welcome email to user
   */
  static async sendWelcomeEmail(email: string, name?: string): Promise<void> {
    const { subject, html, text } = getWelcomeEmailTemplate(name);

    const result = await sendEmail({
      to: email,
      subject,
      html,
      text,
    });

    if (!result.success) {
      throw new EmailSendError(result.error || "Failed to send welcome email");
    }
  }

  /**
   * Check if user's email is verified
   */
  static async isEmailVerified(userId: string): Promise<{
    isVerified: boolean;
    verifiedAt: Date | null;
  }> {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: {
        emailVerified: true,
      },
    });

    return {
      isVerified: !!user.emailVerified,
      verifiedAt: user.emailVerified,
    };
  }
}
