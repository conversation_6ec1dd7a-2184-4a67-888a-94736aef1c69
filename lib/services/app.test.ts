import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { AppStatus } from "@prisma/client";

import { AppService } from "./app";

import { prisma } from "@/lib/db";
import { getAppAnalytics as getAnalyticsData } from "@/lib/analytics";
import {
  AuthenticationError,
  InvalidAppCredentialsError,
  AppNotActiveError,
} from "@/lib/errors";

describe("AppService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("createApp", () => {
    const mockUser = {
      id: "user-123",
      roles: ["MODEL_PROVIDER"],
    };

    const mockCreatedApp = {
      id: "app-123",
      name: "Test App",
      appId: "app_mock-id-16",
      appSecret: "secret_mock-id-32",
      description: "Test Description",
      status: AppStatus.ACTIVE,
      userId: "user-123",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully create an app", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(prisma.app.create).mockResolvedValue(mockCreatedApp);

      const result = await AppService.createApp(
        "user-123",
        "Test App",
        "Test Description",
      );

      expect(prisma.user.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "user-123" },
        select: { id: true, roles: true },
      });
      expect(prisma.app.create).toHaveBeenCalledWith({
        data: {
          userId: "user-123",
          name: "Test App",
          appId: "app_mock-id-16",
          appSecret: "secret_mock-id-32",
          description: "Test Description",
          status: AppStatus.ACTIVE,
        },
        select: {
          id: true,
          name: true,
          appId: true,
          appSecret: true,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result).toEqual({
        ...mockCreatedApp,
        impressions: 0,
        clicks: 0,
        revenue: 0,
        ctr: "0.00",
      });
    });

    it("should create app without description", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(prisma.app.create).mockResolvedValue({
        ...mockCreatedApp,
        description: null,
      });

      const result = await AppService.createApp("user-123", "Test App");

      expect(prisma.app.create).toHaveBeenCalledWith({
        data: {
          userId: "user-123",
          name: "Test App",
          appId: "app_mock-id-16",
          appSecret: "secret_mock-id-32",
          description: null,
          status: AppStatus.ACTIVE,
        },
        select: {
          id: true,
          name: true,
          appId: true,
          appSecret: true,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result.description).toBeNull();
    });

    it("should throw AuthenticationError for non-model-provider user", async () => {
      const nonModelProviderUser = { id: "user-123", roles: ["ADVERTISER"] };

      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(
        nonModelProviderUser,
      );

      await expect(
        AppService.createApp("user-123", "Test App"),
      ).rejects.toThrow(AuthenticationError);
      expect(prisma.app.create).not.toHaveBeenCalled();
    });

    it("should handle database errors", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(prisma.app.create).mockRejectedValue(
        new Error("Database error"),
      );

      await expect(
        AppService.createApp("user-123", "Test App"),
      ).rejects.toThrow("Database error");
    });
  });

  describe("getAppById", () => {
    const mockApp = {
      id: "app-123",
      name: "Test App",
      appId: "app_123",
      appSecret: "secret_123",
      description: "Test Description",
      status: AppStatus.ACTIVE,
      userId: "user-123",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockAnalytics = {
      impressions: 100,
      clicks: 5,
      revenue: 25.5,
      ctr: "5.00",
    };

    it("should successfully get app by ID with secret", async () => {
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);
      vi.mocked(getAnalyticsData).mockResolvedValue(mockAnalytics);

      const result = await AppService.getAppById("app-123", true);

      expect(prisma.app.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "app-123" },
        select: {
          id: true,
          name: true,
          appId: true,
          appSecret: true,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(getAnalyticsData).toHaveBeenCalledWith("app-123");
      expect(result).toEqual({
        ...mockApp,
        ...mockAnalytics,
      });
    });

    it("should get app without secret by default", async () => {
      const appWithoutSecret = { ...mockApp, appSecret: false };

      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(
        appWithoutSecret,
      );
      vi.mocked(getAnalyticsData).mockResolvedValue(mockAnalytics);

      const result = await AppService.getAppById("app-123");

      expect(prisma.app.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "app-123" },
        select: {
          id: true,
          name: true,
          appId: true,
          appSecret: false,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(result.appSecret).toBeUndefined();
    });

    it("should handle app not found", async () => {
      vi.mocked(prisma.app.findUniqueOrThrow).mockRejectedValue(
        new Error("App not found"),
      );

      await expect(AppService.getAppById("non-existent")).rejects.toThrow(
        "App not found",
      );
    });
  });

  describe("getUserApps", () => {
    const mockApps = [
      {
        id: "app-1",
        name: "App 1",
        appId: "app_1",
        description: "App 1 Description",
        status: AppStatus.ACTIVE,
        userId: "user-123",
        createdAt: new Date(),
        updatedAt: new Date(),
        impressions: 100,
        clicks: 5,
        revenue: 25,
        ctr: "5.00",
      },
      {
        id: "app-2",
        name: "App 2",
        appId: "app_2",
        description: "App 2 Description",
        status: AppStatus.SUSPENDED,
        userId: "user-123",
        createdAt: new Date(),
        updatedAt: new Date(),
        impressions: 50,
        clicks: 2,
        revenue: 10,
        ctr: "4.00",
      },
    ];

    it("should successfully get apps by user ID", async () => {
      const mockDbApps = mockApps.map(
        ({ impressions, clicks, revenue, ctr, ...app }) => app,
      );

      vi.mocked(prisma.app.findMany).mockResolvedValue(mockDbApps);
      vi.mocked(getAnalyticsData)
        .mockResolvedValueOnce({
          impressions: 100,
          clicks: 5,
          revenue: 25,
          ctr: "5.00",
        })
        .mockResolvedValueOnce({
          impressions: 50,
          clicks: 2,
          revenue: 10,
          ctr: "4.00",
        });

      const result = await AppService.getUserApps("user-123");

      expect(prisma.app.findMany).toHaveBeenCalledWith({
        where: { userId: "user-123" },
        select: {
          id: true,
          name: true,
          appId: true,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { createdAt: "desc" },
      });
      expect(result).toEqual(mockApps);
    });

    it("should return empty array when no apps found", async () => {
      vi.mocked(prisma.app.findMany).mockResolvedValue([]);

      const result = await AppService.getUserApps("user-123");

      expect(result).toEqual([]);
    });
  });

  describe("updateApp", () => {
    const mockUpdateData = {
      name: "Updated App",
      description: "Updated Description",
      status: AppStatus.SUSPENDED,
    };

    const mockUpdatedApp = {
      id: "app-123",
      name: "Updated App",
      appId: "app_123",
      description: "Updated Description",
      status: AppStatus.SUSPENDED,
      userId: "user-123",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should successfully update an app", async () => {
      vi.mocked(prisma.app.update).mockResolvedValue(mockUpdatedApp);
      vi.mocked(getAnalyticsData).mockResolvedValue({
        impressions: 50,
        clicks: 2,
        revenue: 10,
        ctr: "4.00",
      });

      const result = await AppService.updateApp("app-123", mockUpdateData);

      expect(prisma.app.update).toHaveBeenCalledWith({
        where: { id: "app-123" },
        data: mockUpdateData,
        select: {
          id: true,
          name: true,
          appId: true,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      expect(getAnalyticsData).toHaveBeenCalledWith("app-123");
      expect(result).toEqual({
        ...mockUpdatedApp,
        impressions: 50,
        clicks: 2,
        revenue: 10,
        ctr: "4.00",
      });
    });

    it("should handle update failure", async () => {
      vi.mocked(prisma.app.update).mockRejectedValue(
        new Error("Update failed"),
      );

      await expect(
        AppService.updateApp("app-123", mockUpdateData),
      ).rejects.toThrow("Update failed");
    });
  });

  describe("deleteApp", () => {
    it("should successfully delete an app", async () => {
      vi.mocked(prisma.app.delete).mockResolvedValue({} as any);

      await AppService.deleteApp("app-123", "user-123");

      expect(prisma.app.delete).toHaveBeenCalledWith({
        where: { id: "app-123", userId: "user-123" },
      });
    });

    it("should handle delete failure", async () => {
      vi.mocked(prisma.app.delete).mockRejectedValue(
        new Error("Delete failed"),
      );

      await expect(AppService.deleteApp("app-123", "user-123")).rejects.toThrow(
        "Delete failed",
      );
    });
  });

  describe("verifyAppCredentials", () => {
    const mockApp = {
      id: "app-123",
      name: "Test App",
      appSecret: "secret-123",
      status: AppStatus.ACTIVE,
      userId: "user-123",
    };

    it("should successfully verify app credentials", async () => {
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);

      const result = await AppService.verifyAppCredentials(
        "app-123",
        "secret-123",
      );

      expect(prisma.app.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { appId: "app-123" },
        select: {
          id: true,
          name: true,
          appSecret: true,
          status: true,
          userId: true,
        },
      });
      expect(result).toEqual({
        id: "app-123",
        name: "Test App",
        status: AppStatus.ACTIVE,
        userId: "user-123",
      });
    });

    it("should throw InvalidAppCredentialsError for wrong secret", async () => {
      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(mockApp);

      await expect(
        AppService.verifyAppCredentials("app-123", "wrong-secret"),
      ).rejects.toThrow(InvalidAppCredentialsError);
    });

    it("should throw AppNotActiveError for inactive app", async () => {
      const inactiveApp = { ...mockApp, status: AppStatus.INACTIVE };

      vi.mocked(prisma.app.findUniqueOrThrow).mockResolvedValue(inactiveApp);

      await expect(
        AppService.verifyAppCredentials("app-123", "secret-123"),
      ).rejects.toThrow(AppNotActiveError);
    });

    it("should handle app not found", async () => {
      vi.mocked(prisma.app.findUniqueOrThrow).mockRejectedValue(
        new Error("App not found"),
      );

      await expect(
        AppService.verifyAppCredentials("non-existent", "secret-123"),
      ).rejects.toThrow("App not found");
    });
  });
});
