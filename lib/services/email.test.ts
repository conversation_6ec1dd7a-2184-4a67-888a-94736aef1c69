import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { TokenType } from "@prisma/client";
import crypto from "crypto";

import { EmailService } from "./email";
import { prisma } from "@/lib/db";
import {
  sendEmail,
  generateVerificationToken,
  getVerificationExpiry,
  EmailData,
} from "@/lib/email";
import {
  getEmailVerificationTemplate,
  getWelcomeEmailTemplate,
} from "@/lib/email-templates";
import { EmailSendError } from "@/lib/errors";

// Mock dependencies
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUniqueOrThrow: vi.fn(),
    },
    userToken: {
      create: vi.fn(),
    },
  },
}));

vi.mock("@/lib/email", () => ({
  sendEmail: vi.fn(),
  generateVerificationToken: vi.fn(),
  getVerificationExpiry: vi.fn(),
}));

vi.mock("@/lib/email-templates", () => ({
  getEmailVerificationTemplate: vi.fn(),
  getWelcomeEmailTemplate: vi.fn(),
}));

vi.mock("crypto", () => ({
  default: {
    createHash: vi.fn(() => ({
      update: vi.fn(() => ({
        digest: vi.fn(),
      })),
    })),
  },
}));

describe("EmailService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("sendGenericEmail", () => {
    const mockEmailData: EmailData = {
      to: "<EMAIL>",
      subject: "Test Subject",
      html: "<p>Test HTML</p>",
      text: "Test Text",
    };

    it("should successfully send email", async () => {
      const mockResult = {
        success: true,
        provider: "gmail",
        messageId: "msg-123",
      };
      vi.mocked(sendEmail).mockResolvedValue(mockResult);

      const result = await EmailService.sendGenericEmail(mockEmailData);

      expect(sendEmail).toHaveBeenCalledWith(mockEmailData);
      expect(result).toEqual({
        provider: "gmail",
        sentDate: expect.any(Date),
      });
    });

    it("should throw EmailSendError when email sending fails", async () => {
      const mockResult = {
        success: false,
        error: "SMTP connection failed",
      };
      vi.mocked(sendEmail).mockResolvedValue(mockResult);

      await expect(EmailService.sendGenericEmail(mockEmailData)).rejects.toThrow(
        EmailSendError
      );
      expect(sendEmail).toHaveBeenCalledWith(mockEmailData);
    });

    it("should throw EmailSendError with default message when no error provided", async () => {
      const mockResult = {
        success: false,
      };
      vi.mocked(sendEmail).mockResolvedValue(mockResult);

      await expect(EmailService.sendGenericEmail(mockEmailData)).rejects.toThrow(
        new EmailSendError("Failed to send email")
      );
    });
  });

  describe("sendVerificationEmail", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
    };

    const mockToken = "verification-token";
    const mockTokenHash = "hashed-token";
    const mockExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);

    beforeEach(() => {
      vi.mocked(generateVerificationToken).mockReturnValue(mockToken);
      vi.mocked(getVerificationExpiry).mockReturnValue(mockExpiry);
      
      const mockCrypto = vi.mocked(crypto.createHash);
      mockCrypto.mockReturnValue({
        update: vi.fn().mockReturnValue({
          digest: vi.fn().mockReturnValue(mockTokenHash),
        }),
      } as any);

      vi.mocked(getEmailVerificationTemplate).mockReturnValue({
        subject: "Verify your email",
        html: "<p>Verification email</p>",
        text: "Verification email",
      });
    });

    it("should successfully send verification email", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(prisma.userToken.create).mockResolvedValue({} as any);
      vi.mocked(sendEmail).mockResolvedValue({
        success: true,
        provider: "gmail",
        messageId: "msg-123",
      });

      await EmailService.sendVerificationEmail("user-123");

      expect(prisma.user.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "user-123" },
        select: { id: true, email: true, name: true },
      });
      expect(generateVerificationToken).toHaveBeenCalled();
      expect(getVerificationExpiry).toHaveBeenCalled();
      expect(crypto.createHash).toHaveBeenCalledWith("sha256");
      expect(prisma.userToken.create).toHaveBeenCalledWith({
        data: {
          userId: "user-123",
          type: TokenType.EMAIL_VERIFICATION,
          token: mockTokenHash,
          expiresAt: mockExpiry,
        },
      });
      expect(getEmailVerificationTemplate).toHaveBeenCalledWith(
        "Test User",
        mockToken
      );
      expect(sendEmail).toHaveBeenCalledWith({
        to: "<EMAIL>",
        subject: "Verify your email",
        html: "<p>Verification email</p>",
        text: "Verification email",
      });
    });

    it("should handle user not found", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockRejectedValue(
        new Error("User not found")
      );

      await expect(EmailService.sendVerificationEmail("non-existent")).rejects.toThrow(
        "User not found"
      );
      expect(prisma.userToken.create).not.toHaveBeenCalled();
      expect(sendEmail).not.toHaveBeenCalled();
    });

    it("should throw EmailSendError when email sending fails", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(prisma.userToken.create).mockResolvedValue({} as any);
      vi.mocked(sendEmail).mockResolvedValue({
        success: false,
        error: "Email sending failed",
      });

      await expect(EmailService.sendVerificationEmail("user-123")).rejects.toThrow(
        EmailSendError
      );
    });
  });

  describe("sendWelcomeEmail", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
    };

    beforeEach(() => {
      vi.mocked(getWelcomeEmailTemplate).mockReturnValue({
        subject: "Welcome!",
        html: "<p>Welcome email</p>",
        text: "Welcome email",
      });
    });

    it("should successfully send welcome email", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(sendEmail).mockResolvedValue({
        success: true,
        provider: "gmail",
        messageId: "msg-123",
      });

      await EmailService.sendWelcomeEmail("user-123");

      expect(prisma.user.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "user-123" },
        select: { id: true, email: true, name: true },
      });
      expect(getWelcomeEmailTemplate).toHaveBeenCalledWith("Test User");
      expect(sendEmail).toHaveBeenCalledWith({
        to: "<EMAIL>",
        subject: "Welcome!",
        html: "<p>Welcome email</p>",
        text: "Welcome email",
      });
    });

    it("should handle user not found", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockRejectedValue(
        new Error("User not found")
      );

      await expect(EmailService.sendWelcomeEmail("non-existent")).rejects.toThrow(
        "User not found"
      );
      expect(sendEmail).not.toHaveBeenCalled();
    });

    it("should throw EmailSendError when email sending fails", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(mockUser);
      vi.mocked(sendEmail).mockResolvedValue({
        success: false,
        error: "Welcome email failed",
      });

      await expect(EmailService.sendWelcomeEmail("user-123")).rejects.toThrow(
        EmailSendError
      );
    });
  });

  describe("isEmailVerified", () => {
    it("should return true for verified email", async () => {
      const verifiedUser = {
        emailVerified: new Date(),
      };
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(verifiedUser);

      const result = await EmailService.isEmailVerified("user-123");

      expect(prisma.user.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: "user-123" },
        select: { emailVerified: true },
      });
      expect(result).toBe(true);
    });

    it("should return false for unverified email", async () => {
      const unverifiedUser = {
        emailVerified: null,
      };
      vi.mocked(prisma.user.findUniqueOrThrow).mockResolvedValue(unverifiedUser);

      const result = await EmailService.isEmailVerified("user-123");

      expect(result).toBe(false);
    });

    it("should handle user not found", async () => {
      vi.mocked(prisma.user.findUniqueOrThrow).mockRejectedValue(
        new Error("User not found")
      );

      await expect(EmailService.isEmailVerified("non-existent")).rejects.toThrow(
        "User not found"
      );
    });
  });
});
